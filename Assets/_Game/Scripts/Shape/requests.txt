Tôi muốn implement thêm tính năng booster Bomb. 
- Khi user bấm sử dụng booster ở UIPanelGame, ShapeSpawner sẽ spawn ra ShapeBomb, sẽ đầy đủ tính năng kéo thả giống hệt như ShapeController. User kéo lên board, thả ra nếu như nằm trong phạm vi valid thì sẽ nổ và xóa hết toàn bộ số pixel trong bán kính chỉ định. 
- Khi sử dụng booster UIPanelGame, sẽ có 1 lựa chọn để hủy sử dụng booster bomb đó

- Hãy suy nghĩ logic ShapeBomb sao để hoạt động song song với ShapeController. Có thể tạo base class hoặc tạo interface để ko ảnh hưởng tới các logic khác

